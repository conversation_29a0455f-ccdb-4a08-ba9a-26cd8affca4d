import axios from 'axios'
import { defineStore } from 'pinia'
import type { DigestAuthParams } from '../Interfaces/auth'
export const useAuthStore = defineStore('auth', () => {
  //登录
  const doLogin = async (useName: string, passWord: string) => {
    try {
      const authParams = await getAuthParams()
    } catch {}
  }
  //登出
  //获取认证参数
  const getAuthParams = async (): Promise<DigestAuthParams> => {
    try {
      const fullUrl = window.location.origin + '/login.cgi'
      console.log('fullUrl', fullUrl)
      const response = await axios.get(fullUrl, {
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      })

      const authHeader = response.headers['www-authenticate']
      console.log('authHeader', authHeader)
      if (!authHeader) {
        throw new Error('No WWW-Authenticate header in response')
      }

      // 解析认证头
      // const params = parseAuthHeader(authHeader)
      return {
        realm: 'Highwmg',
        nonce: '5475',
        qop: 'auth',
      }
    } catch (error) {
      console.error('Failed to get auth parameters:', error)
      throw error
    }
  }

  //解析认证头
  // const parseAuthHeader = (header: string): DigestAuthParams => {
  //   const getParam=(str:string)=>{
  //     const arr=str.split('=')
  //   }
  // }
})
